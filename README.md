# RiderService SDK

This SDK provides functionalities related to rider services.

## Modules

- `app`: Contains the main application.
- `riderservice`: Contains the core service logic.

## 连接分析系统

### 概述
SDK集成了统一的连接分析系统，用于监控和分析所有连接相关的活动，包括BLE、WiFi和TCP连接。

### 核心组件
- **ConnectionAnalytics**: 统一的连接分析管理器
- **ConnectionMetrics**: 连接指标数据类
- **AnalyticsListener**: 分析事件监听器接口

### 日志标签标准化
所有连接分析日志统一使用 `CONNECTION_ANALYTICS` 标签，格式为：
```
CONNECTION_ANALYTICS: [连接类型]: [事件描述] ::[时间戳]
```

### 使用示例
```kotlin
// 记录连接开始
val sessionId = ConnectionAnalytics.recordConnectionStart(ConnectionType.BLE, deviceAddress)

// 记录连接成功
ConnectionAnalytics.recordConnectionSuccess(sessionId, connectionInfo)

// 记录连接失败
ConnectionAnalytics.recordConnectionFailure(sessionId, error)

// 使用专用日志函数
logBleConnection("Device connected", deviceAddress)
logWifiConnection("Connected to network", ssid, "AP Mode")
```

## Usage

Further details on usage will be provided here.

## Key Parameters

Details about key parameters for configuring the SDK will be added here.