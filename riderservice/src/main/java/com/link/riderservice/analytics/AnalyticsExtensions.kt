package com.link.riderservice.analytics

import com.link.riderservice.utils.TimeUtils
import com.link.riderservice.utils.logD
import com.link.riderservice.utils.logE
import com.link.riderservice.utils.logI
import com.link.riderservice.utils.logW

/**
 * 连接分析专用的日志扩展函数
 * 提供统一的连接分析日志记录方法
 * <AUTHOR>
 * @date 2024/01/01
 */

// ==================== 连接分析专用日志函数 ====================

/**
 * 记录连接分析调试信息
 * @param message 日志消息
 */
fun logConnectionD(message: String) {
    logD(ConnectionAnalytics.TAG, "$message ::${TimeUtils.getCurrentTimeStr()}")
}

/**
 * 记录连接分析一般信息
 * @param message 日志消息
 */
fun logConnectionI(message: String) {
    logI(ConnectionAnalytics.TAG, "$message ::${TimeUtils.getCurrentTimeStr()}")
}

/**
 * 记录连接分析警告信息
 * @param message 日志消息
 */
fun logConnectionW(message: String) {
    logW(ConnectionAnalytics.TAG, "$message ::${TimeUtils.getCurrentTimeStr()}")
}

/**
 * 记录连接分析错误信息
 * @param message 日志消息
 * @param throwable 异常信息
 */
fun logConnectionE(message: String, throwable: Throwable? = null) {
    logE(ConnectionAnalytics.TAG, "$message ::${TimeUtils.getCurrentTimeStr()}", throwable)
}

// ==================== 特定连接类型的日志函数 ====================

/**
 * 记录BLE连接分析日志
 * @param message 日志消息
 * @param deviceAddress 设备地址
 */
fun logBleConnection(message: String, deviceAddress: String? = null) {
    val fullMessage = buildString {
        append("BLE: $message")
        deviceAddress?.let { append(", Device: $it") }
    }
    logConnectionD(fullMessage)
}

/**
 * 记录WiFi连接分析日志
 * @param message 日志消息
 * @param ssid WiFi SSID
 * @param mode 连接模式（AP/P2P）
 */
fun logWifiConnection(message: String, ssid: String? = null, mode: String? = null) {
    val fullMessage = buildString {
        append("WiFi")
        mode?.let { append(" $it") }
        append(": $message")
        ssid?.let { append(", SSID: $it") }
    }
    logConnectionD(fullMessage)
}

/**
 * 记录TCP连接分析日志
 * @param message 日志消息
 * @param host 主机地址
 * @param port 端口号
 */
fun logTcpConnection(message: String, host: String? = null, port: Int? = null) {
    val fullMessage = buildString {
        append("TCP: $message")
        if (host != null && port != null) {
            append(", Address: $host:$port")
        }
    }
    logConnectionD(fullMessage)
}

// ==================== 连接状态变化日志函数 ====================

/**
 * 记录连接状态变化
 * @param type 连接类型
 * @param oldStatus 旧状态
 * @param newStatus 新状态
 * @param deviceId 设备ID
 */
fun logConnectionStatusChange(
    type: ConnectionType,
    oldStatus: ConnectionStatus,
    newStatus: ConnectionStatus,
    deviceId: String? = null
) {
    val message = buildString {
        append("Status change: $type")
        deviceId?.let { append(" ($it)") }
        append(" $oldStatus -> $newStatus")
    }
    logConnectionI(message)
}

/**
 * 记录连接重试
 * @param type 连接类型
 * @param retryCount 重试次数
 * @param deviceId 设备ID
 * @param reason 重试原因
 */
fun logConnectionRetry(
    type: ConnectionType,
    retryCount: Int,
    deviceId: String? = null,
    reason: String? = null
) {
    val message = buildString {
        append("Retry #$retryCount: $type")
        deviceId?.let { append(" ($it)") }
        reason?.let { append(", Reason: $it") }
    }
    logConnectionW(message)
}

/**
 * 记录连接错误
 * @param type 连接类型
 * @param error 错误信息
 * @param deviceId 设备ID
 */
fun logConnectionError(
    type: ConnectionType,
    error: ConnectionError,
    deviceId: String? = null
) {
    val message = buildString {
        append("Error: $type")
        deviceId?.let { append(" ($it)") }
        append(" - ${error.message} (Code: ${error.code})")
    }
    logConnectionE(message, error.cause)
}

// ==================== 性能相关日志函数 ====================

/**
 * 记录连接性能指标
 * @param type 连接类型
 * @param duration 连接时长（毫秒）
 * @param deviceId 设备ID
 * @param additionalInfo 额外信息
 */
fun logConnectionPerformance(
    type: ConnectionType,
    duration: Long,
    deviceId: String? = null,
    additionalInfo: String? = null
) {
    val message = buildString {
        append("Performance: $type")
        deviceId?.let { append(" ($it)") }
        append(" completed in ${duration}ms")
        additionalInfo?.let { append(", $it") }
    }
    logConnectionI(message)
}

/**
 * 记录连接统计信息
 * @param type 连接类型
 * @param successCount 成功次数
 * @param failureCount 失败次数
 * @param averageDuration 平均连接时长
 */
fun logConnectionStats(
    type: ConnectionType,
    successCount: Int,
    failureCount: Int,
    averageDuration: Long
) {
    val totalAttempts = successCount + failureCount
    val successRate = if (totalAttempts > 0) {
        (successCount.toFloat() / totalAttempts * 100).toInt()
    } else 0
    
    val message = "Stats: $type - Success: $successCount, Failed: $failureCount, " +
            "Rate: $successRate%, Avg Duration: ${averageDuration}ms"
    logConnectionI(message)
}
