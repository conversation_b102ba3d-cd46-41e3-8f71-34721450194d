package com.link.riderservice.connection.ble

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.location.LocationManager
import android.os.ParcelUuid
import com.link.riderservice.api.RiderService
import com.link.riderservice.ble.data.Data
import com.link.riderservice.ble.scanner.BluetoothLeScannerCompat
import com.link.riderservice.ble.scanner.ScanCallback
import com.link.riderservice.ble.scanner.ScanFilter
import com.link.riderservice.ble.scanner.ScanResult
import com.link.riderservice.ble.scanner.ScanSettings
import com.link.riderservice.ext.collectWithScope
import com.link.riderservice.utils.BleUtils
import com.link.riderservice.utils.TimeUtils
import com.link.riderservice.utils.logD
import com.link.riderservice.utils.logE
import com.link.riderservice.utils.logW
import com.link.riderservice.utils.mainScope
import com.link.riderservice.analytics.ConnectionAnalytics
import com.link.riderservice.analytics.ConnectionType
import com.link.riderservice.analytics.ConnectionInfo
import com.link.riderservice.analytics.ConnectionError
import com.link.riderservice.analytics.ErrorType
import com.link.riderservice.analytics.logConnectionPerformance
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import java.util.UUID

/**
 * <AUTHOR>
 * @date 2022/8/25
 */
internal class RiderBleManager private constructor() {
    private val discoveredBleDevices: MutableList<BleDevice> = ArrayList()
    private var bleManager: BleManagerImpl? = null
    private var connectedBleDevice: BleDevice? = null
    private var isScanning = false
    private var isConfigurationConnected = false
    private var isAutoConnectionEnabled = true
    private val riderBleCallbacks: MutableList<WeakReference<RiderBleCallback>> = mutableListOf()
    private var lastScanTimestamp: Long = 0
    private var delayScanJob: Job? = null

    // 连接分析相关
    private var currentConnectionSessionId: String? = null
    private var scanSessionId: String? = null
    private var connectionStartTime: Long = 0
    
    // 使用 lazy 属性，避免重复获取
    private val configPrefs by lazy { RiderService.instance.getConfigPreferences() }

    @Synchronized
    fun addCallback(callback: RiderBleCallback) {
        riderBleCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    fun removeCallback(callback: RiderBleCallback) {
        riderBleCallbacks.removeIf { it.get() == callback }
    }

    fun isConfigConnect(): Boolean {
        return isConfigurationConnected
    }

    fun getConnectAddress(): String {
        return connectedBleDevice?.device?.address ?: ""
    }

    fun connect(device: BleDevice, context: Context) {
        stopScan()
        if (isConnected()) {
            logD(TAG, "Already connected, ignoring connect request")
            return
        }

        // 记录连接开始
        connectionStartTime = System.currentTimeMillis()
        currentConnectionSessionId = ConnectionAnalytics.recordConnectionStart(
            ConnectionType.BLE,
            device.device.address
        )

        connectedBleDevice = device
        bleManager = BleManagerImpl(context, device.device, createBleManagerCallback())
        bleManager?.connect()
    }

    private fun createBleManagerCallback(): BleManagerCallback {
        return object : BleManagerCallback {
            override fun onDataReceived(device: BluetoothDevice, data: Data) {
                riderBleCallbacks.forEach { it.get()?.onDataReceived(device, data) }
            }

            override fun onDeviceConnecting(device: BluetoothDevice) {
                com.link.riderservice.analytics.logBleConnection("Start BLE connect", device.address)
                riderBleCallbacks.forEach { it.get()?.onDeviceConnecting(device) }
            }

            override fun onDeviceConnected(device: BluetoothDevice) {
                // 记录连接成功
                currentConnectionSessionId?.let { sessionId ->
                    val connectionInfo = ConnectionInfo(
                        extras = mapOf(
                            "deviceName" to (device.name ?: "Unknown"),
                            "bondState" to device.bondState.toString()
                        )
                    )
                    ConnectionAnalytics.recordConnectionSuccess(sessionId, connectionInfo)

                    // 记录性能指标
                    val duration = System.currentTimeMillis() - connectionStartTime
                    logConnectionPerformance(ConnectionType.BLE, duration, device.address)
                }

                com.link.riderservice.analytics.logBleConnection("End BLE connect", device.address)
                riderBleCallbacks.forEach { it.get()?.onDeviceConnected(device) }
            }

            override fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int) {
                // 记录连接失败
                currentConnectionSessionId?.let { sessionId ->
                    val error = ConnectionError(
                        code = reason,
                        message = "BLE connection failed with reason: $reason",
                        type = ErrorType.DEVICE_NOT_FOUND
                    )
                    ConnectionAnalytics.recordConnectionFailure(sessionId, error)
                }
                currentConnectionSessionId = null

                riderBleCallbacks.forEach { it.get()?.onDeviceFailedToConnect(device, reason) }
            }

            override fun onDeviceReady(device: BluetoothDevice) {
                riderBleCallbacks.forEach { it.get()?.onDeviceReady(device) }
            }

            override fun onDeviceDisconnecting(device: BluetoothDevice) {
                riderBleCallbacks.forEach { it.get()?.onDeviceDisconnecting(device) }
            }

            override fun onDeviceDisconnected(device: BluetoothDevice, reason: Int) {
                logD(TAG, "Device disconnected with reason: $reason")

                // 清理连接会话
                currentConnectionSessionId = null

                riderBleCallbacks.forEach { it.get()?.onDeviceDisconnected(device, reason) }
            }

            override fun onWriteRequestFailed(device: BluetoothDevice, status: Int) {
                logD(TAG, "Write request failed with status: $status")
            }

            override fun onEnableNotificationFailed(device: BluetoothDevice, status: Int) {
                logE(TAG, "Enable notification failed with status: $status")
                riderBleCallbacks.forEach { it.get()?.onEnableNotificationFailed(device, status) }
            }
        }
    }

    fun write(value: ByteArray) {
        bleManager?.write(value)
    }

    fun release(isManualRelease: Boolean = true) {
        logD(TAG, "Releasing BLE manager, manual: $isManualRelease")
        if (isManualRelease) {
            connectedBleDevice = null
            stopScan()
        }
        cancelDelayedScan()
        releaseManager()
    }

    private fun cancelDelayedScan() {
        delayScanJob?.cancel()
        delayScanJob = null
    }

    private fun releaseManager() {
        bleManager?.release()
        bleManager = null
    }

    private fun clearDevices() {
        discoveredBleDevices.clear()
        riderBleCallbacks.forEach { it.get()?.onScanResult(discoveredBleDevices) }
    }

    fun closeConnect() {
        logD(TAG, "Force closing connection")
        releaseManager()
        isAutoConnectionEnabled = false
    }

    fun isConnected(): Boolean {
        return bleManager?.isConnected == true
    }

    /**
     * 启动扫描（优化版本）
     * @param enableAutoConnection 是否启用自动连接
     */
    fun startScan(enableAutoConnection: Boolean = true) {
        this.isAutoConnectionEnabled = enableAutoConnection
        
        // 检查是否有配置的设备地址
        if (isAutoConnectionEnabled && !hasConfiguredDevices()) {
            logD(TAG, "No BLE address configured, skipping scan")
            return
        }

        // 检查连接状态
        if (isConnected()) {
            logD(TAG, "BLE already connected, returning cached devices")
            riderBleCallbacks.forEach { it.get()?.onScanResult(discoveredBleDevices) }
            return
        }

        // 检查权限和硬件状态
        val permissionCheck = checkPermissionsAndHardware()
        if (permissionCheck != PermissionCheckResult.GRANTED) {
            handlePermissionCheckResult(permissionCheck)
            return
        }

        // 检查扫描频率限制
        val now = System.currentTimeMillis()
        val timeSinceLastScan = now - lastScanTimestamp
        
        when {
            isScanning -> handleAlreadyScanning(now, timeSinceLastScan)
            timeSinceLastScan < SLICE_SCANNING_PERIOD_MS -> handleTooFrequentScan(timeSinceLastScan)
            else -> {
                lastScanTimestamp = now
                startScanInternal()
            }
        }
    }

    /**
     * 检查是否有配置的设备
     */
    private fun hasConfiguredDevices(): Boolean {
        return configPrefs.getBleAddress() != null || configPrefs.getScanBleAddress() != null
    }

    @SuppressLint("MissingPermission")
    private fun connectBestDevice(discoveredBleDevices: List<BleDevice>) {
        // 根据不同连接策略获取设备
        val connectionStrategy = determineConnectionStrategy()
        
        val deviceToConnect = when (connectionStrategy) {
            is ConnectionStrategy.Manual -> {
                logD(TAG, "Manual scan connect: ${connectionStrategy.address}")
                discoveredBleDevices.find { it.device.address == connectionStrategy.address }
                    .also { configPrefs.removeScanBleAddress() }
            }
            is ConnectionStrategy.Configured -> {
                logD(TAG, "Config auto connect: ${connectionStrategy.address}")
                discoveredBleDevices.find { it.device.address == connectionStrategy.address }
            }
            is ConnectionStrategy.Automatic -> {
                logD(TAG, "Auto connect nearest device")
                findNearestDevice(discoveredBleDevices)
            }
        }
        
        // 执行连接
        deviceToConnect?.let { device ->
            isConfigurationConnected = connectionStrategy is ConnectionStrategy.Configured
            connect(device, RiderService.instance.getApplication())
        } ?: logW(TAG, "No suitable device found for strategy: ${connectionStrategy::class.simpleName}")
    }

    /**
     * 确定连接策略
     */
    private fun determineConnectionStrategy(): ConnectionStrategy {
        return when {
            configPrefs.containsScanBleAddress() -> ConnectionStrategy.Manual(configPrefs.getScanBleAddress())
            configPrefs.containsBleAddress() -> ConnectionStrategy.Configured(configPrefs.getBleAddress())
            else -> ConnectionStrategy.Automatic
        }
    }

    private val bluetoothStateBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.STATE_OFF)
            val previousState = intent.getIntExtra(
                BluetoothAdapter.EXTRA_PREVIOUS_STATE, BluetoothAdapter.STATE_OFF
            )
            when (state) {
                BluetoothAdapter.STATE_ON -> {
                    logD(TAG, "Bluetooth is on")
                    startScan()
                }

                BluetoothAdapter.STATE_TURNING_OFF, BluetoothAdapter.STATE_OFF -> {
                    logD(TAG, "Bluetooth is off")
                    if (previousState != BluetoothAdapter.STATE_TURNING_OFF && previousState != BluetoothAdapter.STATE_OFF) {
                        stopScan()
                    }
                }
            }
        }
    }

    private val locationProviderChangedReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {

        }
    }

    private val scanCallback = object : ScanCallback() {
        override fun onScanResult(callbackType: Int, result: ScanResult) {
            super.onScanResult(callbackType, result)
            if (!isNoise(result)) {
                flow {
                    deviceDiscovered(result)
                    emit(discoveredBleDevices.filter {
                        matchesUuidFilter(it.scanResult) && matchesNearbyFilter(it.rssi)
                    })
                }.collectWithScope(mainScope) { bleDevices ->
                    riderBleCallbacks.forEach {
                        it.get()?.onScanResult(bleDevices)
                    }
                }
            }
        }

        override fun onBatchScanResults(results: MutableList<ScanResult>) {
            super.onBatchScanResults(results)

            if (results.isEmpty()) return
            flow {
                results.filter { !isNoise(it) }.forEach {
                    deviceDiscovered(it)
                }
                emit(discoveredBleDevices.filter {
                    matchesUuidFilter(it.scanResult) && matchesNearbyFilter(it.rssi)
                })
            }.collectWithScope(mainScope) { bleDevices ->
                logD(TAG, "onBatchScanResults: ${bleDevices.size} discoveredBleDevices found")
                riderBleCallbacks.forEach {
                    it.get()?.onScanResult(bleDevices)
                }
                if (isAutoConnectionEnabled) {
                    connectBestDevice(bleDevices)
                }
            }
        }

        override fun onScanFailed(errorCode: Int) {
            super.onScanFailed(errorCode)
            logW(TAG, "Scanning failed with code $errorCode")
        }
    }

    private fun startScanInternal() {
        // 记录扫描开始
        scanSessionId = ConnectionAnalytics.recordConnectionStart(
            ConnectionType.BLE,
            "SCAN_SESSION"
        )

        com.link.riderservice.analytics.logBleConnection("Start BLE scan")
        val settings = ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
            .setReportDelay(TIMEOUT_SCAN)
            .setUseHardwareBatchingIfSupported(false)
            .build()
        val filter = ScanFilter.Builder()
            .setManufacturerData(MANUFACTURE_ID, MANUFACTURE_DATA)
            .build()
        val filters = listOf(filter)
        val scanner = BluetoothLeScannerCompat.getScanner()
        scanner.startScan(filters, settings, scanCallback)
        clearDevices()
        isScanning = true
        riderBleCallbacks.forEach { it.get()?.onScanning() }
    }

    private fun delayScan(delay: Long) {
        cancelDelayedScan()
        delayScanJob = mainScope.launch(Dispatchers.IO) {
            delay(delay)
            startScan()
        }
    }

    fun stopScan() {
        // 记录扫描结束
        scanSessionId?.let { sessionId ->
            val connectionInfo = ConnectionInfo(
                extras = mapOf(
                    "devicesFound" to discoveredBleDevices.size.toString(),
                    "scanDuration" to (System.currentTimeMillis() - lastScanTimestamp).toString()
                )
            )
            ConnectionAnalytics.recordConnectionSuccess(sessionId, connectionInfo)
        }
        scanSessionId = null

        com.link.riderservice.analytics.logBleConnection("End BLE scan")
        if (isScanning) {
            val scanner = BluetoothLeScannerCompat.getScanner()
            scanner.stopScan(scanCallback)
        }
        isScanning = false
        riderBleCallbacks.forEach { it.get()?.onScanFinish() }
    }

    private fun findNearestDevice(discoveredBleDevices: List<BleDevice>): BleDevice? {
        val sortedDevices = discoveredBleDevices.sortedByDescending { it.rssi }
        
        return when {
            sortedDevices.isEmpty() -> {
                logD(TAG, "No devices found")
                null
            }
            sortedDevices.size == 1 -> {
                val device = sortedDevices.first()
                if (device.rssi > MIN_DEVICE_RSSI) {
                    logD(TAG, "Single device found with RSSI: ${device.rssi}dBm")
                    device
                } else {
                    logD(TAG, "Single device RSSI too weak: ${device.rssi}dBm")
                    null
                }
            }
            else -> {
                val firstDevice = sortedDevices[0]
                val secondDevice = sortedDevices[1]
                val rssiDifference = firstDevice.rssi - secondDevice.rssi

                logD(TAG, "Multiple devices found. Best: ${firstDevice.rssi}dBm, " +
                    "Second: ${secondDevice.rssi}dBm, Diff: ${rssiDifference}dBm")
                
                if (rssiDifference > MIN_RSSI_DIFFERENCE && firstDevice.rssi > MIN_DEVICE_RSSI) {
                    logD(TAG, "Clear winner found: ${firstDevice.device.address}")
                    firstDevice
                } else {
                    logD(TAG, "No clear winner, skipping auto-connect")
                    null
                }
            }
        }
    }

    private fun isNoise(result: ScanResult): Boolean {
        return !result.isConnectable || 
               result.rssi < FILTER_RSSI || 
               BleUtils.isBeacon(result) || 
               BleUtils.isAirDrop(result)
    }

    @Synchronized
    private fun deviceDiscovered(result: ScanResult) {
        val index = discoveredBleDevices.indexOfFirst { it.device.address == result.device.address }
        if (index == -1) {
            discoveredBleDevices.add(BleDevice(result))
        } else {
            discoveredBleDevices[index] = discoveredBleDevices[index].update(result)
        }
    }

    private fun matchesUuidFilter(result: ScanResult): Boolean {
        if (!FILTER_UUID_REQUIRED) return true
        val record = result.scanRecord ?: return false
        val uuids = record.serviceUuids ?: return false
        return uuids.contains(FILTER_SERVICE_UUID)
    }

    private fun matchesNearbyFilter(rssi: Int): Boolean {
        return if (!FILTER_NEARBY_ONLY) true else (rssi in FILTER_RSSI..-1)
    }

    fun registerBroadcastReceivers(context: Context) {
        context.registerReceiver(
            bluetoothStateBroadcastReceiver, 
            IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED)
        )
        context.registerReceiver(
            locationProviderChangedReceiver, 
            IntentFilter(LocationManager.MODE_CHANGED_ACTION)
        )
    }

    fun unregisterBroadcastReceivers(context: Context) {
        try {
            context.unregisterReceiver(bluetoothStateBroadcastReceiver)
            context.unregisterReceiver(locationProviderChangedReceiver)
        } catch (ignore: Exception) {
            logW(TAG, "Failed to unregister broadcast receivers", ignore)
        }
    }

    fun getCurrentConnectDevice(): BleDevice? {
        return connectedBleDevice
    }

    companion object {
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            RiderBleManager()
        }

        // 过滤器配置常量
        private const val FILTER_UUID_REQUIRED: Boolean = false
        private const val FILTER_NEARBY_ONLY: Boolean = true
        private const val FILTER_RSSI = -100 // [dBm]
        private val FILTER_SERVICE_UUID =
            ParcelUuid(UUID.fromString("0000ff00-0000-1000-8000-00805f9b34fb"))

        // 制造商数据配置
        private const val MANUFACTURE_ID = 0x0AE7
        private val MANUFACTURE_DATA = byteArrayOf(0x72, 0x6C)

        // 扫描配置常量
        private const val TIMEOUT_SCAN = 1_200L
        private const val NUM_SCAN_DURATIONS_KEPT = 5
        private const val EXCESSIVE_SCANNING_PERIOD_MS = 30 * 1000L
        private const val SLICE_SCANNING_PERIOD_MS = 6 * 1000L
        private const val SCANNING_DURATION = 10 * 1000L
        private const val SCHEDULE_START_SCAN_WHEN_STARTED_TOO_FREQUENTLY = true

        // 设备连接配置
        private const val MIN_DEVICE_RSSI = -48
        private const val MIN_RSSI_DIFFERENCE = 10

        private const val TAG = "RiderBleManager"
    }

    /**
     * 检查权限和硬件状态
     */
    private fun checkPermissionsAndHardware(): PermissionCheckResult {
        return when {
            !BleUtils.isBleEnabled() -> PermissionCheckResult.BLUETOOTH_DISABLED
            !BleUtils.isLocationPermissionGranted(RiderService.instance.getApplication()) -> 
                PermissionCheckResult.LOCATION_PERMISSION_NEEDED
            BleUtils.isSorAbove() && !BleUtils.isBluetoothScanPermissionGranted(RiderService.instance.getApplication()) -> 
                PermissionCheckResult.BLUETOOTH_SCAN_PERMISSION_NEEDED
            else -> PermissionCheckResult.GRANTED
        }
    }

    /**
     * 处理权限检查结果
     */
    private fun handlePermissionCheckResult(result: PermissionCheckResult) {
        when (result) {
            PermissionCheckResult.BLUETOOTH_DISABLED -> 
                riderBleCallbacks.forEach { it.get()?.onRequestBt() }
            PermissionCheckResult.LOCATION_PERMISSION_NEEDED -> 
                riderBleCallbacks.forEach { it.get()?.onNeedLocationPermission() }
            PermissionCheckResult.BLUETOOTH_SCAN_PERMISSION_NEEDED -> 
                riderBleCallbacks.forEach { it.get()?.onNeedBluetoothScanPermission() }
            PermissionCheckResult.GRANTED -> { /* 已处理 */ }
        }
    }

    /**
     * 处理已在扫描的情况
     */
    private fun handleAlreadyScanning(now: Long, timeSinceLastScan: Long) {
        logD(TAG, "BLE already scanning")
        if (timeSinceLastScan > SCANNING_DURATION) {
            stopScan()
            lastScanTimestamp = now
            startScanInternal()
        } else {
            riderBleCallbacks.forEach { it.get()?.onScanning() }
        }
    }

    /**
     * 处理扫描过于频繁的情况
     */
    private fun handleTooFrequentScan(timeSinceLastScan: Long) {
        logD(TAG, "StartScan too frequent, ${timeSinceLastScan}ms since last scan")
        if (SCHEDULE_START_SCAN_WHEN_STARTED_TOO_FREQUENTLY) {
            val delay = SLICE_SCANNING_PERIOD_MS - timeSinceLastScan
            logD(TAG, "Scheduling delayed scan after ${delay}ms")
            delayScan(delay)
        }
    }

    /**
     * 权限检查结果枚举
     */
    private enum class PermissionCheckResult {
        GRANTED,
        BLUETOOTH_DISABLED,
        LOCATION_PERMISSION_NEEDED,
        BLUETOOTH_SCAN_PERMISSION_NEEDED
    }

    /**
     * 连接策略密封类
     */
    private sealed class ConnectionStrategy {
        data class Manual(val address: String?) : ConnectionStrategy()
        data class Configured(val address: String?) : ConnectionStrategy()
        object Automatic : ConnectionStrategy()
    }
}