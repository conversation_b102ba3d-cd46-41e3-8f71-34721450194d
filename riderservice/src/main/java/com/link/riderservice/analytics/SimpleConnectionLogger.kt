package com.link.riderservice.analytics

import android.content.Context
import com.link.riderservice.BuildConfig
import com.link.riderservice.utils.TimeUtils
import com.link.riderservice.utils.logD
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 简单的连接时间记录器
 * 只在Debug模式下工作，记录连接时间并生成报表到cache目录
 * <AUTHOR>
 * @date 2024/01/01
 */
object SimpleConnectionLogger : AnalyticsListener {
    
    private const val TAG = "SimpleConnectionLogger"
    private const val REPORT_FILE_NAME = "connection_times_report.txt"
    
    /** 连接时间记录列表 */
    private val connectionRecords = CopyOnWriteArrayList<ConnectionTimeRecord>()
    
    /** 应用上下文 */
    private var appContext: Context? = null
    
    /**
     * 初始化
     */
    fun init(context: Context) {
        if (BuildConfig.DEBUG) {
            appContext = context.applicationContext
            logD(TAG, "SimpleConnectionLogger initialized in DEBUG mode")
        }
    }
    
    // ==================== AnalyticsListener 实现 ====================
    
    override fun onConnectionStarted(metrics: ConnectionMetrics) {
        // 连接开始时不需要记录，等连接完成时一起记录
    }
    
    override fun onConnectionCompleted(metrics: ConnectionMetrics) {
        if (!BuildConfig.DEBUG || !metrics.isCompleted) return
        
        val record = ConnectionTimeRecord(
            timestamp = System.currentTimeMillis(),
            connectionType = metrics.connectionType.name,
            deviceId = metrics.deviceId,
            duration = metrics.duration,
            isSuccess = metrics.isSuccessful,
            errorMessage = metrics.error?.message
        )
        
        connectionRecords.add(record)
        logD(TAG, "Recorded connection: ${record.connectionType} - ${record.deviceId} (${record.duration}ms)")
        
        // 每记录10次连接就生成一次报表
        if (connectionRecords.size % 10 == 0) {
            generateReport()
        }
    }
    
    override fun onConnectionRetry(metrics: ConnectionMetrics, retryCount: Int) {
        // 简化版本不记录重试
    }
    
    // ==================== 报表生成 ====================
    
    /**
     * 生成连接时间报表到cache目录
     */
    fun generateReport() {
        if (!BuildConfig.DEBUG || appContext == null || connectionRecords.isEmpty()) {
            return
        }
        
        try {
            val cacheDir = appContext!!.cacheDir
            val reportFile = File(cacheDir, REPORT_FILE_NAME)
            
            FileWriter(reportFile, false).use { writer ->
                writer.write(generateReportContent())
            }
            
            logD(TAG, "Connection report generated: ${reportFile.absolutePath}")
        } catch (e: Exception) {
            logD(TAG, "Failed to generate report: ${e.message}")
        }
    }
    
    /**
     * 生成报表内容
     */
    private fun generateReportContent(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        
        return buildString {
            appendLine("=== 连接时间报表 ===")
            appendLine("生成时间: ${TimeUtils.getCurrentTimeStr()}")
            appendLine("总记录数: ${connectionRecords.size}")
            appendLine()
            
            // 按连接类型分组统计
            val groupedRecords = connectionRecords.groupBy { it.connectionType }
            
            groupedRecords.forEach { (type, records) ->
                val successRecords = records.filter { it.isSuccess }
                val failedRecords = records.filter { !it.isSuccess }
                
                appendLine("【$type 连接】")
                appendLine("  总次数: ${records.size}")
                appendLine("  成功: ${successRecords.size}")
                appendLine("  失败: ${failedRecords.size}")
                
                if (successRecords.isNotEmpty()) {
                    val avgTime = successRecords.map { it.duration }.average().toLong()
                    val minTime = successRecords.minOf { it.duration }
                    val maxTime = successRecords.maxOf { it.duration }
                    
                    appendLine("  平均时间: ${avgTime}ms")
                    appendLine("  最快时间: ${minTime}ms")
                    appendLine("  最慢时间: ${maxTime}ms")
                }
                appendLine()
            }
            
            // 详细记录
            appendLine("=== 详细记录 ===")
            connectionRecords.takeLast(50).forEach { record ->
                val timeStr = dateFormat.format(Date(record.timestamp))
                val status = if (record.isSuccess) "成功" else "失败"
                val error = if (record.errorMessage != null) " (${record.errorMessage})" else ""
                
                appendLine("$timeStr | ${record.connectionType} | ${record.deviceId} | ${record.duration}ms | $status$error")
            }
            
            if (connectionRecords.size > 50) {
                appendLine("... (显示最近50条记录)")
            }
        }
    }
    
    /**
     * 清除记录
     */
    fun clearRecords() {
        if (BuildConfig.DEBUG) {
            connectionRecords.clear()
            logD(TAG, "Connection records cleared")
        }
    }
    
    /**
     * 获取记录数量
     */
    fun getRecordCount(): Int {
        return if (BuildConfig.DEBUG) connectionRecords.size else 0
    }
    
    /**
     * 手动生成报表
     */
    fun forceGenerateReport() {
        if (BuildConfig.DEBUG) {
            generateReport()
        }
    }
}

/**
 * 连接时间记录数据类
 */
data class ConnectionTimeRecord(
    val timestamp: Long,
    val connectionType: String,
    val deviceId: String,
    val duration: Long,
    val isSuccess: Boolean,
    val errorMessage: String? = null
)
