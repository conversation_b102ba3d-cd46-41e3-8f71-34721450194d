package com.link.riderservice.connection.network

import android.Manifest
import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkInfo
import android.net.NetworkRequest
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiManager
import android.net.wifi.WifiNetworkSpecifier
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import com.link.riderservice.api.RiderService
import com.link.riderservice.utils.TimeUtils
import com.link.riderservice.analytics.ConnectionAnalytics
import com.link.riderservice.analytics.ConnectionType
import com.link.riderservice.analytics.ConnectionInfo
import com.link.riderservice.analytics.ConnectionError
import com.link.riderservice.analytics.ErrorType
import com.link.riderservice.analytics.logConnectionPerformance
import com.link.riderservice.analytics.logConnectionRetry
import java.net.Inet4Address

/**
 * WiFi客户端管理器
 * 实现连接到仪表端WiFi热点功能，支持Android 6.0-15版本
 */
internal class WiFiClientManager(
    private val listener: WiFiClientManagerListener
) {
    private val context = RiderService.instance.getApplication().applicationContext
    private val wifiManager: WifiManager by lazy {
        context.getSystemService(Context.WIFI_SERVICE) as WifiManager
    }
    private val connectivityManager: ConnectivityManager by lazy {
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    private var currentState = WifiClientState.WIFI_CLIENT_START
    private var targetSsid: String = ""
    private var targetPassword: String = ""
    private var currentNetwork: Network? = null
    private var reconnectionAttempts = 0
    private val reconnectionHandler = Handler(Looper.getMainLooper())

    // 连接分析相关
    private var currentConnectionSessionId: String? = null
    private var connectionStartTime: Long = 0

    companion object {
        private const val TAG = "WiFiClientManager"
        private const val MAX_RECONNECTION_ATTEMPTS = 5
        private const val RECONNECTION_FAILED = -999
    }

    // WiFi状态广播接收器
    private val wifiStateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    handleWifiStateChanged(intent)
                }
                WifiManager.NETWORK_STATE_CHANGED_ACTION -> {
                    handleNetworkStateChanged(intent)
                }
                ConnectivityManager.CONNECTIVITY_ACTION -> {
                    handleConnectivityChanged()
                }
            }
        }
    }

    // 网络回调（Android 6.0+）
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            Log.d(TAG, "Network available: $network")
            
            currentNetwork = network
            
            // 绑定应用到此网络（Android 6.0+）
            try {
                connectivityManager.bindProcessToNetwork(network)
            } catch (e: Exception) {
                Log.w(TAG, "Failed to bind process to network", e)
            }

            val ipAddress = getNetworkIpAddress(network)
            updateState(WifiClientState.CONNECTED)
            
            // 验证连接有效性
            updateState(WifiClientState.VALIDATING_CONNECTION)
            if (validateConnection(ipAddress)) {
                updateState(WifiClientState.CONNECTION_VALIDATED)
                reconnectionAttempts = 0 // 重置重连计数

                // 记录连接成功
                currentConnectionSessionId?.let { sessionId ->
                    val connectionInfo = ConnectionInfo(
                        ipAddress = ipAddress,
                        ssid = targetSsid,
                        extras = mapOf(
                            "networkId" to network.toString(),
                            "connectionMode" to "AP"
                        )
                    )
                    ConnectionAnalytics.recordConnectionSuccess(sessionId, connectionInfo)

                    // 记录性能指标
                    val duration = System.currentTimeMillis() - connectionStartTime
                    logConnectionPerformance(ConnectionType.WIFI_AP, duration, targetSsid)
                }

                com.link.riderservice.analytics.logWifiConnection("Wi-Fi connected, IP: $ipAddress", targetSsid, "AP Mode")
                listener.onWifiConnected(targetSsid, ipAddress)
            } else {
                updateState(WifiClientState.CONNECTION_INVALID)

                // 记录连接失败
                currentConnectionSessionId?.let { sessionId ->
                    val error = ConnectionError(
                        code = -2,
                        message = "WiFi connection validation failed",
                        type = ErrorType.AUTHENTICATION_FAILED
                    )
                    ConnectionAnalytics.recordConnectionFailure(sessionId, error)
                }

                com.link.riderservice.analytics.logWifiConnection("Wi-Fi connection invalid", targetSsid, "AP Mode")
                listener.onWifiConnectionFailed(-2)
            }
        }
        
        override fun onLost(network: Network) {
            super.onLost(network)
            Log.d(TAG, "Network lost: $network")
            com.link.riderservice.analytics.logWifiConnection("Wi-Fi disconnected", targetSsid, "AP Mode")
            
            if (currentNetwork == network) {
                currentNetwork = null
                updateState(WifiClientState.DISCONNECTED)
                listener.onWifiDisconnected()
                
                // 启动重连
                startReconnection()
            }
        }
        
        override fun onUnavailable() {
            super.onUnavailable()
            Log.d(TAG, "Network unavailable")

            // 记录连接失败
            currentConnectionSessionId?.let { sessionId ->
                val error = ConnectionError(
                    code = -1,
                    message = "WiFi network unavailable",
                    type = ErrorType.NETWORK_UNAVAILABLE
                )
                ConnectionAnalytics.recordConnectionFailure(sessionId, error)
            }
            currentConnectionSessionId = null

            com.link.riderservice.analytics.logWifiConnection("Wi-Fi connection unavailable", targetSsid, "AP Mode")
            updateState(WifiClientState.CONNECT_FAILED)
            listener.onWifiConnectionFailed(-1)
        }
        
        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities)
            Log.d(TAG, "Network capabilities changed: $networkCapabilities")
        }
    }

    /**
     * 连接到指定WiFi网络
     */
    fun connectToWifi(ssid: String, password: String) {
        Log.d(TAG, "Connecting to WiFi: $ssid")

        // 记录连接开始
        connectionStartTime = System.currentTimeMillis()
        currentConnectionSessionId = ConnectionAnalytics.recordConnectionStart(
            ConnectionType.WIFI_AP,
            ssid
        )

        com.link.riderservice.analytics.logWifiConnection("Attempting to connect", ssid, "AP Mode")

        targetSsid = ssid
        targetPassword = password
        
        if (!checkPermissions()) {
            Log.e(TAG, "Missing required permissions")
            updateState(WifiClientState.PERMISSION_DENIED)
            listener.onWifiConnectionFailed(-2)
            return
        }
        
        if (!wifiManager.isWifiEnabled) {
            Log.e(TAG, "WiFi is not enabled")
            updateState(WifiClientState.WIFI_DISABLED)
            listener.onWifiState(false)
            return
        }
        
        // 检查是否已连接到目标网络
        if (currentState == WifiClientState.CONNECTED && getCurrentSsid() == ssid) {
            Log.d(TAG, "Already connected to target network")
            return
        }
        
        updateState(WifiClientState.CHECKING_PERMISSIONS)
        
        if (!checkPermissions()) {
            updateState(WifiClientState.PERMISSION_DENIED)
            listener.onWifiConnectionFailed(-1)
            return
        }
        
        updateState(WifiClientState.WIFI_STATE_CHECK)
        updateState(WifiClientState.CONNECTING)
        listener.onWifiConnecting(ssid)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            connectToWifiModern(ssid, password)
        } else {
            connectToWifiLegacy(ssid, password)
        }
    }

    /**
     * Android 10+ 使用新的网络请求API
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    private fun connectToWifiModern(ssid: String, password: String) {
        try {
            val wifiNetworkSpecifier = WifiNetworkSpecifier.Builder()
                .setSsid(ssid)
                .setWpa2Passphrase(password)
                .build()

            val networkRequest = NetworkRequest.Builder()
                .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
                .setNetworkSpecifier(wifiNetworkSpecifier)
                .build()

            // 先取消之前的请求
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback)
            } catch (e: Exception) {
                // 忽略未注册的异常
            }

            connectivityManager.requestNetwork(networkRequest, networkCallback)
            Log.d(TAG, "Modern WiFi connection request sent")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to connect using modern API", e)
            com.link.riderservice.analytics.logWifiConnection("Modern API connection failed: ${e.localizedMessage}", targetSsid, "AP Mode")
            updateState(WifiClientState.CONNECT_FAILED)
            listener.onWifiConnectionFailed(-3)
        }
    }

    /**
     * Android 6.0-9 使用传统WifiConfiguration方式
     */
    @SuppressLint("MissingPermission")
    private fun connectToWifiLegacy(ssid: String, password: String) {
        try {
            // 先扫描网络，确认目标网络存在
            startWifiScanForTarget(ssid) { found ->
                if (found) {
                    connectToWifiLegacyDirect(ssid, password)
                } else {
                    Log.e(TAG, "Target network not found in scan")
                    com.link.riderservice.analytics.logWifiConnection("Legacy API scan failed", targetSsid, "AP Mode")
                    updateState(WifiClientState.SCAN_FAILED)
                    listener.onWifiConnectionFailed(-4)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to connect using legacy API", e)
            com.link.riderservice.analytics.logWifiConnection("Legacy API connection failed: ${e.localizedMessage}", targetSsid, "AP Mode")
            updateState(WifiClientState.CONNECT_FAILED)
            listener.onWifiConnectionFailed(-5)
        }
    }

    @SuppressLint("MissingPermission")
    private fun connectToWifiLegacyDirect(ssid: String, password: String) {
        val wifiConfig = WifiConfiguration().apply {
            SSID = "\"$ssid\""
            preSharedKey = "\"$password\""
            allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
            allowedProtocols.set(WifiConfiguration.Protocol.RSN)
            allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
            allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
        }

        // 移除可能存在的相同配置
        val existingConfigs = wifiManager.configuredNetworks
        existingConfigs?.forEach { config ->
            if (config.SSID == "\"$ssid\"") {
                wifiManager.removeNetwork(config.networkId)
            }
        }

        val networkId = wifiManager.addNetwork(wifiConfig)
        if (networkId != -1) {
            Log.d(TAG, "WiFi configuration added, networkId: $networkId")
            
            wifiManager.disconnect()
            Thread.sleep(500) // 短暂等待断开完成
            
            val enabled = wifiManager.enableNetwork(networkId, true)
            if (enabled) {
                wifiManager.reconnect()
                Log.d(TAG, "WiFi reconnect requested")
            } else {
                Log.e(TAG, "Failed to enable network")
                com.link.riderservice.analytics.logWifiConnection("Legacy API enableNetwork failed", targetSsid, "AP Mode")
                updateState(WifiClientState.CONNECT_FAILED)
                listener.onWifiConnectionFailed(-6)
            }
        } else {
            Log.e(TAG, "Failed to add WiFi configuration")
            com.link.riderservice.analytics.logWifiConnection("Legacy API addNetwork failed", targetSsid, "AP Mode")
            updateState(WifiClientState.CONNECT_FAILED)
            listener.onWifiConnectionFailed(-7)
        }
    }

    /**
     * 扫描WiFi网络
     */
    @SuppressLint("MissingPermission")
    private fun startWifiScanForTarget(targetSsid: String, callback: (Boolean) -> Unit) {
        updateState(WifiClientState.SCANNING)
        
        val scanReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                if (intent.action == WifiManager.SCAN_RESULTS_AVAILABLE_ACTION) {
                    val scanResults = wifiManager.scanResults
                    val found = scanResults.any { it.SSID == targetSsid }
                    
                    Log.d(TAG, "Scan completed, target network found: $found")
                    listener.onNetworkScanComplete(scanResults)
                    
                    context.unregisterReceiver(this)
                    callback(found)
                }
            }
        }
        
        val filter = IntentFilter(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION)
        context.registerReceiver(scanReceiver, filter)
        
        val scanStarted = wifiManager.startScan()
        if (!scanStarted) {
            Log.w(TAG, "Failed to start WiFi scan")
            context.unregisterReceiver(scanReceiver)
            callback(false)
        }
    }

    /**
     * 断开WiFi连接
     */
    fun disconnect() {
        Log.d(TAG, "Disconnecting WiFi")
        
        try {
            connectivityManager.bindProcessToNetwork(null)

            connectivityManager.unregisterNetworkCallback(networkCallback)
        } catch (e: Exception) {
            Log.w(TAG, "Error during disconnect", e)
        }
        
        currentNetwork = null
        updateState(WifiClientState.DISCONNECTED)
    }

    /**
     * 获取当前连接的SSID
     */
    @SuppressLint("MissingPermission")
    private fun getCurrentSsid(): String {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            wifiInfo?.ssid?.replace("\"", "") ?: ""
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current SSID", e)
            ""
        }
    }

    /**
     * 获取网络IP地址
     */
    private fun getNetworkIpAddress(network: Network): String {
        return try {
            val linkProperties = connectivityManager.getLinkProperties(network)
            linkProperties?.linkAddresses?.find { 
                it.address is Inet4Address 
            }?.address?.hostAddress ?: ""
        } catch (e: Exception) {
            Log.e(TAG, "Error getting IP address", e)
            ""
        }
    }

    /**
     * 启动重连机制
     */
    private fun startReconnection() {
        if (currentState == WifiClientState.RECONNECTING || 
            targetSsid.isEmpty() || 
            reconnectionAttempts >= MAX_RECONNECTION_ATTEMPTS) {
            
            if (reconnectionAttempts >= MAX_RECONNECTION_ATTEMPTS) {
                Log.e(TAG, "Max reconnection attempts reached, giving up")
                updateState(WifiClientState.CONNECTION_FAILED)
                listener.onWifiConnectionFailed(RECONNECTION_FAILED)
            }
            return
        }
        
        updateState(WifiClientState.RECONNECTING)
        reconnectionAttempts++

        // 记录重连尝试
        currentConnectionSessionId?.let { sessionId ->
            ConnectionAnalytics.recordConnectionRetry(sessionId, reconnectionAttempts)
        }
        logConnectionRetry(ConnectionType.WIFI_AP, reconnectionAttempts, targetSsid, "Network lost")

        val delay = calculateBackoffDelay(reconnectionAttempts)
        Log.d(TAG, "Scheduling reconnection attempt $reconnectionAttempts/$MAX_RECONNECTION_ATTEMPTS in ${delay}ms")
        
        reconnectionHandler.postDelayed({
            if (reconnectionAttempts <= MAX_RECONNECTION_ATTEMPTS && 
                currentState == WifiClientState.RECONNECTING) {
                
                Log.d(TAG, "Reconnection attempt: $reconnectionAttempts/$MAX_RECONNECTION_ATTEMPTS")
                connectToWifi(targetSsid, targetPassword)
            } else if (reconnectionAttempts > MAX_RECONNECTION_ATTEMPTS) {
                Log.e(TAG, "Max reconnection attempts reached")
                updateState(WifiClientState.CONNECTION_FAILED)
                listener.onWifiConnectionFailed(RECONNECTION_FAILED)
            }
        }, delay)
    }

    private fun calculateBackoffDelay(attempt: Int): Long {
        // 指数退避：1s, 2s, 4s, 8s, 16s
        return minOf(1000L * (1 shl (attempt - 1)), 16000L)
    }

    /**
     * 验证网络连接有效性
     */
    private fun validateConnection(ipAddress: String): Boolean {
        if (ipAddress.isEmpty()) {
            Log.w(TAG, "IP address is empty")
            return false
        }
        
        // 检查IP地址格式
        try {
            val parts = ipAddress.split(".")
            if (parts.size != 4) return false
            parts.forEach { 
                val num = it.toInt()
                if (num < 0 || num > 255) return false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Invalid IP address format: $ipAddress", e)
            return false
        }
        
        return true
    }

    /**
     * 检查必要权限
     */
    fun checkPermissions(): Boolean {
        val fineLocation = ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        
        val nearbyWifiDevices = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context, Manifest.permission.NEARBY_WIFI_DEVICES
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }
        
        return fineLocation && nearbyWifiDevices
    }

    /**
     * 检查WiFi是否启用
     */
    fun isWifiEnabled(): Boolean {
        return wifiManager.isWifiEnabled
    }

    /**
     * 获取当前连接状态
     */
    fun getCurrentState(): WifiClientState {
        return currentState
    }

    /**
     * 获取重连尝试次数
     */
    fun getReconnectionAttempts(): Int {
        return reconnectionAttempts
    }

    /**
     * 获取当前连接会话ID
     */
    fun getCurrentSessionId(): String? {
        return currentConnectionSessionId
    }

    private fun handleWifiStateChanged(intent: Intent) {
        val state = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)
        when (state) {
            WifiManager.WIFI_STATE_DISABLED -> {
                Log.d(TAG, "WiFi disabled")
                listener.onWifiState(false)
                updateState(WifiClientState.WIFI_DISABLED)
            }
            WifiManager.WIFI_STATE_ENABLED -> {
                Log.d(TAG, "WiFi enabled")
                listener.onWifiState(true)
            }
        }
    }

    private fun handleNetworkStateChanged(intent: Intent) {
        val networkInfo = intent.getParcelableExtra<NetworkInfo>(WifiManager.EXTRA_NETWORK_INFO)
        Log.d(TAG, "Network state changed: ${networkInfo?.state}")
        
        // 这里主要用于Android 6.0以下版本的网络状态监听
    }

    @SuppressLint("MissingPermission", "DefaultLocale")
    private fun getWifiIpAddress(): String {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            val ipInt = wifiInfo.ipAddress
            String.format(
                "%d.%d.%d.%d",
                ipInt and 0xff,
                ipInt shr 8 and 0xff,
                ipInt shr 16 and 0xff,
                ipInt shr 24 and 0xff
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting WiFi IP", e)
            ""
        }
    }

    private fun handleConnectivityChanged() {
        // 处理网络连接变化
        val networkInfo = connectivityManager.activeNetworkInfo
        Log.d(TAG, "Connectivity changed: ${networkInfo?.isConnected}")
    }

    private fun updateState(newState: WifiClientState) {
        Log.d(TAG, "State changed: $currentState -> $newState")
        currentState = newState
    }

    /**
     * 初始化
     */
    init {
        // 注册广播接收器
        val filter = IntentFilter().apply {
            addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
            addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION)
            addAction(ConnectivityManager.CONNECTIVITY_ACTION)
        }
        context.registerReceiver(wifiStateReceiver, filter)
    }

    /**
     * 重置连接状态（用于手动重试）
     */
    fun resetConnectionState() {
        Log.d(TAG, "Resetting connection state")
        reconnectionAttempts = 0
        reconnectionHandler.removeCallbacksAndMessages(null)
        if (currentState == WifiClientState.RECONNECTING || 
            currentState == WifiClientState.CONNECTION_FAILED) {
            updateState(WifiClientState.WIFI_CLIENT_START)
        }
    }



    /**
     * 销毁资源
     */
    fun destroy() {
        try {
            context.unregisterReceiver(wifiStateReceiver)
        } catch (e: Exception) {
            Log.e(TAG, "Error unregistering receiver", e)
        }
        
        try {
            connectivityManager.unregisterNetworkCallback(networkCallback)
        } catch (e: Exception) {
            Log.e(TAG, "Error unregistering network callback", e)
        }
        
        reconnectionHandler.removeCallbacksAndMessages(null)
        disconnect()
    }
}

/**
 * WiFi客户端连接状态
 */
enum class WifiClientState {
    WIFI_CLIENT_START,      // 初始化状态
    CHECKING_PERMISSIONS,   // 检查权限
    PERMISSION_DENIED,      // 权限被拒绝
    WIFI_STATE_CHECK,       // 检查WiFi状态
    WIFI_DISABLED,          // WiFi未开启
    SCANNING,               // 扫描网络中
    NETWORK_FOUND,          // 找到目标网络
    SCAN_FAILED,            // 扫描失败
    SCAN_TIMEOUT,           // 扫描超时
    CONNECTING,             // 连接中
    CONNECTED,              // 已连接
    VALIDATING_CONNECTION,  // 验证连接
    CONNECTION_VALIDATED,   // 连接验证通过
    CONNECTION_INVALID,     // 连接无效
    DISCONNECTED,           // 已断开
    CONNECT_FAILED,         // 连接失败
    CONNECT_TIMEOUT,        // 连接超时
    RECONNECTING,           // 重连中
    RECONNECT_FAILED,       // 重连失败
    CONNECTION_FAILED       // 连接彻底失败
} 