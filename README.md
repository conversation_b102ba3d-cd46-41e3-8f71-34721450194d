# RiderService SDK

This SDK provides functionalities related to rider services.

## Modules

- `app`: Contains the main application.
- `riderservice`: Contains the core service logic.

## 连接分析系统

### 概述
SDK集成了统一的连接分析系统，用于监控和分析所有连接相关的活动，包括BLE、WiFi和TCP连接。

### 核心组件
- **ConnectionAnalytics**: 统一的连接分析管理器
- **ConnectionMetrics**: 连接指标数据类
- **AnalyticsListener**: 分析事件监听器接口

### 日志标签标准化
所有连接分析日志统一使用 `CONNECTION_ANALYTICS` 标签，格式为：
```
CONNECTION_ANALYTICS: [连接类型]: [事件描述] ::[时间戳]
```

### 使用示例
```kotlin
// 记录连接开始
val sessionId = ConnectionAnalytics.recordConnectionStart(ConnectionType.BLE, deviceAddress)

// 记录连接成功
ConnectionAnalytics.recordConnectionSuccess(sessionId, connectionInfo)

// 记录连接失败
ConnectionAnalytics.recordConnectionFailure(sessionId, error)

// 使用专用日志函数
logBleConnection("Device connected", deviceAddress)
logWifiConnection("Connected to network", ssid, "AP Mode")
```

## 性能监控系统

### 概述
SDK集成了先进的性能监控系统，实时跟踪和分析连接性能指标，提供数据驱动的优化建议。

### 核心功能
- **实时性能监控**: 自动收集连接时间、成功率等关键指标
- **智能性能分析**: 基于历史数据提供性能建议和警告
- **多维度报告**: 支持摘要报告、详细报告和JSON格式导出
- **性能等级评估**: 自动评估连接性能等级(A+到D)

### 性能指标
- **连接成功率**: 成功连接次数 / 总尝试次数
- **平均连接时间**: 成功连接的平均耗时
- **连接时间范围**: 最快和最慢连接时间
- **重试统计**: 连接重试次数和模式
- **实时指标**: 当前活跃连接和近期成功率

### 使用示例
```kotlin
// 获取性能摘要报告
val summaryReport = connectionManager.getPerformanceReport()

// 获取详细性能报告
val detailedReport = connectionManager.getDetailedPerformanceReport()

// 获取特定连接类型的报告
val bleReport = connectionManager.getConnectionTypePerformanceReport(ConnectionType.BLE)

// 获取连接成功率
val successRate = connectionManager.getConnectionSuccessRate(ConnectionType.WIFI_AP)

// 获取性能建议
val recommendations = connectionManager.getPerformanceRecommendations(ConnectionType.BLE)

// 清除性能数据
connectionManager.clearPerformanceData()
```

### 性能报告示例
```
=== 连接性能摘要 ===
时间: 2024-01-01 12:00:00

【BLE】
  总尝试: 25, 成功: 23, 失败: 2, 成功率: 92%, 平均时间: 1250ms, 最快: 800ms, 最慢: 2100ms
  状态: 优秀

【WIFI_AP】
  总尝试: 15, 成功: 12, 失败: 3, 成功率: 80%, 平均时间: 3200ms, 最快: 2100ms, 最慢: 5500ms
  状态: 良好
```

### 性能警告和建议
系统会自动检测以下性能问题并提供建议：
- **连接时间过长**: 超过5秒的连接会触发警告
- **成功率偏低**: 低于70%的成功率会提供优化建议
- **重试次数过多**: 超过3次重试会记录警告
- **网络环境问题**: 基于错误类型提供针对性建议

## Usage

Further details on usage will be provided here.

## Key Parameters

Details about key parameters for configuring the SDK will be added here.