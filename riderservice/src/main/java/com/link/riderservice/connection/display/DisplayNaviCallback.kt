package com.link.riderservice.connection.display

import android.view.Display

/**
 * <AUTHOR>
 * @date 2022/8/25
 */
interface DisplayNaviCallback {
    /**
     * 平台成功连接
     */
    fun onDeviceConnected()

    /**
     * 平台断开连接
     */
    fun onDeviceDisconnected()

    /**
     *  初始化 Display
     *  @param display
     */
    fun onDisplayInitialized(display: Display)

    /**
     *  释放 Display
     */
    fun onDisplayReleased(display: Display)

    /**
     * 视频频道准备好，可以投屏
     */
    fun onVideoChannelReady()
    fun onRequestMediaProjection()
    fun onMirrorStart()
    fun onMirrorStop()

    /**
     * TCP连接失败
     * @param reason 失败原因代码
     */
    fun onTcpConnectionFailed(reason: Int) {}
}