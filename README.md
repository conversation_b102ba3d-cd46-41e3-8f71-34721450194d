# RiderService SDK

This SDK provides functionalities related to rider services.

## Modules

- `app`: Contains the main application.
- `riderservice`: Contains the core service logic.

## 连接分析系统

### 概述
SDK集成了统一的连接分析系统，用于监控和分析所有连接相关的活动，包括BLE、WiFi和TCP连接。

### 核心组件
- **ConnectionAnalytics**: 统一的连接分析管理器
- **ConnectionMetrics**: 连接指标数据类
- **AnalyticsListener**: 分析事件监听器接口

### 日志标签标准化
所有连接分析日志统一使用 `CONNECTION_ANALYTICS` 标签，格式为：
```
CONNECTION_ANALYTICS: [连接类型]: [事件描述] ::[时间戳]
```

### 使用示例
```kotlin
// 记录连接开始
val sessionId = ConnectionAnalytics.recordConnectionStart(ConnectionType.BLE, deviceAddress)

// 记录连接成功
ConnectionAnalytics.recordConnectionSuccess(sessionId, connectionInfo)

// 记录连接失败
ConnectionAnalytics.recordConnectionFailure(sessionId, error)

// 使用专用日志函数
logBleConnection("Device connected", deviceAddress)
logWifiConnection("Connected to network", ssid, "AP Mode")
```

## 连接时间监控系统（仅Debug模式）

### 概述
SDK集成了简单的连接时间监控系统，仅在Debug模式下工作，记录每次连接的时间并生成报表到app的cache目录。

### 核心功能
- **连接时间记录**: 自动记录每次连接的耗时
- **Debug模式限制**: 只在BuildConfig.DEBUG为true时工作
- **简单报表生成**: 生成文本格式的连接时间报表
- **Cache目录存储**: 报表自动保存到app的cache目录

### 使用示例
```kotlin
// 初始化连接时间记录器（在Application中调用）
connectionManager.initDebugLogger(this)

// 手动生成连接时间报表
connectionManager.generateConnectionTimeReport()

// 获取当前记录数量
val recordCount = connectionManager.getConnectionTimeRecordCount()

// 清除连接时间记录
connectionManager.clearConnectionTimeRecords()
```

### 报表示例
```
=== 连接时间报表 ===
生成时间: 2024-01-01 12:00:00
总记录数: 25

【BLE 连接】
  总次数: 15
  成功: 13
  失败: 2
  平均时间: 1250ms
  最快时间: 800ms
  最慢时间: 2100ms

【WIFI_AP 连接】
  总次数: 10
  成功: 8
  失败: 2
  平均时间: 3200ms
  最快时间: 2100ms
  最慢时间: 5500ms

=== 详细记录 ===
2024-01-01 12:00:01 | BLE | AA:BB:CC:DD:EE:FF | 1250ms | 成功
2024-01-01 12:00:05 | WIFI_AP | MyWiFi | 3200ms | 成功
...
```

### 特性说明
- **自动记录**: 连接完成时自动记录时间和结果
- **定期生成**: 每记录10次连接自动生成一次报表
- **文件位置**: 报表保存在 `{app_cache_dir}/connection_times_report.txt`
- **Debug限制**: 只在Debug构建中工作，Release版本不会记录任何数据

## Usage

Further details on usage will be provided here.

## Key Parameters

Details about key parameters for configuring the SDK will be added here.