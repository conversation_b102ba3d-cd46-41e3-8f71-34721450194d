package com.link.riderservice.analytics

import com.link.riderservice.utils.TimeUtils
import com.link.riderservice.utils.logD
import com.link.riderservice.utils.logE
import com.link.riderservice.utils.logI
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 统一连接分析管理器
 * 负责收集、管理和分析所有连接相关的数据
 * <AUTHOR>
 * @date 2024/01/01
 */
object ConnectionAnalytics {
    
    /** 统一的连接分析日志标签 */
    const val TAG = "CONNECTION_ANALYTICS"
    
    /** 当前活跃的连接指标 */
    private val activeConnections = ConcurrentHashMap<String, ConnectionMetrics>()
    
    /** 已完成的连接历史记录 */
    private val connectionHistory = CopyOnWriteArrayList<ConnectionMetrics>()
    
    /** 分析监听器列表 */
    private val analyticsListeners = CopyOnWriteArrayList<AnalyticsListener>()
    
    /** 最大历史记录数量 */
    private const val MAX_HISTORY_SIZE = 1000
    
    init {
        // 添加默认的日志监听器
        addListener(SimpleAnalyticsListener())
        logI(TAG, "ConnectionAnalytics initialized")
    }
    
    // ==================== 公共API ====================
    
    /**
     * 记录连接开始
     * @param type 连接类型
     * @param deviceId 设备ID
     * @return 会话ID，用于后续操作
     */
    fun recordConnectionStart(type: ConnectionType, deviceId: String): String {
        val sessionId = generateSessionId()
        val metrics = ConnectionMetrics(
            sessionId = sessionId,
            connectionType = type,
            deviceId = deviceId,
            startTime = System.currentTimeMillis()
        )
        
        activeConnections[sessionId] = metrics
        logD(TAG, "Connection started: Type=$type, Device=$deviceId, Session=$sessionId, Time=${TimeUtils.getCurrentTimeStr()}")
        
        notifyListeners { it.onConnectionStarted(metrics) }
        return sessionId
    }
    
    /**
     * 记录连接成功
     * @param sessionId 会话ID
     * @param connectionInfo 连接信息
     */
    fun recordConnectionSuccess(sessionId: String, connectionInfo: ConnectionInfo? = null) {
        activeConnections[sessionId]?.let { metrics ->
            val updatedMetrics = metrics.copy(
                endTime = System.currentTimeMillis(),
                status = ConnectionStatus.SUCCESS,
                connectionInfo = connectionInfo
            )
            
            completeConnection(sessionId, updatedMetrics)
            logD(TAG, "Connection succeeded: Type=${updatedMetrics.connectionType}, " +
                    "Device=${updatedMetrics.deviceId}, Duration=${updatedMetrics.duration}ms, " +
                    "Time=${TimeUtils.getCurrentTimeStr()}")
        } ?: run {
            logE(TAG, "Cannot record success for unknown session: $sessionId")
        }
    }
    
    /**
     * 记录连接失败
     * @param sessionId 会话ID
     * @param error 错误信息
     */
    fun recordConnectionFailure(sessionId: String, error: ConnectionError) {
        activeConnections[sessionId]?.let { metrics ->
            val updatedMetrics = metrics.copy(
                endTime = System.currentTimeMillis(),
                status = ConnectionStatus.FAILED,
                error = error
            )
            
            completeConnection(sessionId, updatedMetrics)
            logE(TAG, "Connection failed: Type=${updatedMetrics.connectionType}, " +
                    "Device=${updatedMetrics.deviceId}, Error=${error.message}, " +
                    "Duration=${updatedMetrics.duration}ms, Time=${TimeUtils.getCurrentTimeStr()}")
        } ?: run {
            logE(TAG, "Cannot record failure for unknown session: $sessionId")
        }
    }
    
    /**
     * 记录连接超时
     * @param sessionId 会话ID
     */
    fun recordConnectionTimeout(sessionId: String) {
        val error = ConnectionError(
            code = -1,
            message = "Connection timeout",
            type = ErrorType.CONNECTION_TIMEOUT
        )
        
        activeConnections[sessionId]?.let { metrics ->
            val updatedMetrics = metrics.copy(
                endTime = System.currentTimeMillis(),
                status = ConnectionStatus.TIMEOUT,
                error = error
            )
            
            completeConnection(sessionId, updatedMetrics)
            logE(TAG, "Connection timeout: Type=${updatedMetrics.connectionType}, " +
                    "Device=${updatedMetrics.deviceId}, Duration=${updatedMetrics.duration}ms")
        }
    }
    
    /**
     * 记录连接重试
     * @param sessionId 会话ID
     * @param retryCount 重试次数
     */
    fun recordConnectionRetry(sessionId: String, retryCount: Int) {
        activeConnections[sessionId]?.let { metrics ->
            val updatedMetrics = metrics.copy(retryCount = retryCount)
            activeConnections[sessionId] = updatedMetrics
            
            logD(TAG, "Connection retry: Type=${metrics.connectionType}, " +
                    "Device=${metrics.deviceId}, Retry=#$retryCount")
            
            notifyListeners { it.onConnectionRetry(updatedMetrics, retryCount) }
        }
    }
    
    /**
     * 取消连接
     * @param sessionId 会话ID
     */
    fun cancelConnection(sessionId: String) {
        activeConnections[sessionId]?.let { metrics ->
            val updatedMetrics = metrics.copy(
                endTime = System.currentTimeMillis(),
                status = ConnectionStatus.CANCELLED
            )
            
            completeConnection(sessionId, updatedMetrics)
            logD(TAG, "Connection cancelled: Type=${updatedMetrics.connectionType}, " +
                    "Device=${updatedMetrics.deviceId}")
        }
    }
    
    // ==================== 监听器管理 ====================
    
    /**
     * 添加分析监听器
     */
    fun addListener(listener: AnalyticsListener) {
        analyticsListeners.add(listener)
    }
    
    /**
     * 移除分析监听器
     */
    fun removeListener(listener: AnalyticsListener) {
        analyticsListeners.remove(listener)
    }
    
    // ==================== 数据查询 ====================
    
    /**
     * 获取活跃连接数量
     */
    fun getActiveConnectionCount(): Int = activeConnections.size
    
    /**
     * 获取指定类型的活跃连接
     */
    fun getActiveConnections(type: ConnectionType? = null): List<ConnectionMetrics> {
        return if (type == null) {
            activeConnections.values.toList()
        } else {
            activeConnections.values.filter { it.connectionType == type }
        }
    }
    
    /**
     * 获取连接历史记录
     */
    fun getConnectionHistory(limit: Int = 100): List<ConnectionMetrics> {
        return connectionHistory.takeLast(limit)
    }
    
    /**
     * 清除历史记录
     */
    fun clearHistory() {
        connectionHistory.clear()
        logI(TAG, "Connection history cleared")
    }
    
    // ==================== 内部方法 ====================
    
    /**
     * 完成连接记录
     */
    private fun completeConnection(sessionId: String, metrics: ConnectionMetrics) {
        activeConnections.remove(sessionId)
        addToHistory(metrics)
        notifyListeners { it.onConnectionCompleted(metrics) }
    }
    
    /**
     * 添加到历史记录
     */
    private fun addToHistory(metrics: ConnectionMetrics) {
        connectionHistory.add(metrics)
        
        // 限制历史记录大小
        while (connectionHistory.size > MAX_HISTORY_SIZE) {
            connectionHistory.removeAt(0)
        }
    }
    
    /**
     * 生成会话ID
     */
    private fun generateSessionId(): String {
        return UUID.randomUUID().toString().substring(0, 8)
    }
    
    /**
     * 通知所有监听器
     */
    private fun notifyListeners(action: (AnalyticsListener) -> Unit) {
        analyticsListeners.forEach { listener ->
            try {
                action(listener)
            } catch (e: Exception) {
                logE(TAG, "Error notifying analytics listener", e)
            }
        }
    }
}
