package com.link.riderservice.connection.display

import android.content.Context
import android.media.projection.MediaProjection
import android.view.Display
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.dto.AutoLinkConnect
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.connection.display.protocol.project.Protos
import com.link.riderservice.connection.display.transport.DataConnection
import com.link.riderservice.connection.display.transport.Transport
import com.link.riderservice.connection.display.transport.wifi.TcpConnection
import com.link.riderservice.connection.display.transport.wifi.TcpServerConnection
import com.link.riderservice.connection.network.SoftIP2pListener
import com.link.riderservice.connection.network.SoftP2pManagerNew
import com.link.riderservice.utils.TimeUtils
import com.link.riderservice.utils.countDownByFlow
import com.link.riderservice.utils.logD
import com.link.riderservice.utils.logE
import com.link.riderservice.utils.mainScope
import kotlinx.coroutines.Job
import java.io.IOException
import java.lang.ref.WeakReference
import java.net.Inet4Address
import java.net.InetAddress
import java.net.NetworkInterface
import java.util.Enumeration

/**
 * <AUTHOR>
 * @date 2022/8/25
 */
internal class DisplayNaviManager(
    requestWifiInfo: () -> Unit,
    onWifiState: (Boolean) -> Unit
) : DataConnection.Callback {
    private var mConnection: DataConnection? = null
    private var mTransport: Transport? = null
    private var countDown: Job? = null
    private val mControl by lazy {
        AutolinkControl(RiderService.instance.getApplication())
    }
    private val mP2pListener = object : SoftIP2pListener {
        override fun requestWifiInfo() {
            requestWifiInfo()
        }

        override fun onWifiState(opened: Boolean) {
            onWifiState(opened)
        }

        override fun onCancelConnect() {
            logD(TAG, "onCancelConnect")
            if (isP2pConnected) {
                shutdown()
            } else {
                requestWifiInfo()
            }
        }

        override fun onWifiConnectSuccess() {
            logD(TAG, "p2p connected for first time")
            logD("connect analysis:", "start autolink connect::${TimeUtils.getCurrentTimeStr()}")
            startConnectionEstablishing()
            isP2pConnected = true
            countDown?.cancel()
            val connect = AutoLinkConnect(getIp())
            RiderService.instance.sendMessageToRiderService(connect)
            waitForAutoLinkConnect(2)
        }

        override fun connectExist() {
            logD(TAG, "p2p already connected")
            isP2pConnected = true
            startConnectionEstablishing()
            countDown?.cancel()
            val connect = AutoLinkConnect(getIp())
            RiderService.instance.sendMessageToRiderService(connect)
            waitForAutoLinkConnect(2)
        }

        override fun onP2pConnectSuccess() {
            logD(TAG, "onP2pConnectSuccess")
            logD("connect analysis:", "tcp connect start::${TimeUtils.getCurrentTimeStr()}")
            startConnectionEstablishing()
        }

        override fun onWifiDisconnect() {
            logD(TAG, "onWifiDisconnect")
            if (isP2pConnected) {
                shutdown()
            }
            isP2pConnected = false
        }
    }
    private val softP2pManager by lazy { SoftP2pManagerNew(mP2pListener) }
    private val mCallbacks: MutableList<WeakReference<DisplayNaviCallback>> = mutableListOf()
    private var mContext: Context? = null
    private var isRequestNotification = false
    private var isP2pConnected = false

    private val mControlListener: AutolinkControl.OnControlListener =
        object : AutolinkControl.OnControlListener {
            override fun onDisplayInitialized(display: Display?) {
                mCallbacks.forEach {
                    display?.let { display ->
                        it.get()?.onDisplayInitialized(display)
                    }
                }
            }

            override fun onDisplayReleased(display: Display) {
                mCallbacks.forEach {
                    it.get()?.onDisplayReleased(display)
                }
            }

            @Deprecated("仪表盘不适用")
            override fun requestLandscape(isLandscape: Boolean) {

            }

            override fun onLoseConnected() {
                logD(TAG, "LoseConnected")
                shutdown()
            }

            override fun onUnrecoverableError(err: Int) {
                logD(TAG, "Received unrecoverable error $err. Shutting down.")
                shutdown()
            }

            override fun onDisconnected() {
                logD(TAG, "onDisconnected")
                shutdown()
            }

            override fun onByeByeRequest(reason: Int) {
                logD(TAG, "received ByeByeRequest with reason $reason")
                shutdown()
            }

            override fun onByeByeResponse() {
                logD(TAG, "received ByeByeResponse")
                shutdown()
            }

            override fun onStatusChanged(status: Int) {
                logD(TAG, "onStatusChanged: $status")
            }

            @Deprecated("仪表盘不适用")
            override fun onRequestAutoRotation(isAutoed: Boolean) {
            }

            override fun stopTransport() {
                mTransport?.stopTransport()
            }

            override fun onVideoChannelReady() {
                mCallbacks.forEach {
                    it.get()?.onVideoChannelReady()
                }
            }

            override fun onRequestMediaProjection() {
                mCallbacks.forEach {
                    it.get()?.onRequestMediaProjection()
                }
            }

            override fun onMirrorStart() {
                mCallbacks.forEach {
                    it.get()?.onMirrorStart()
                }
            }

            override fun onMirrorStop() {
                mCallbacks.forEach {
                    it.get()?.onMirrorStop()
                }
            }
        }

    /**
     * 增加回调
     * @param callback 回调
     * @see DisplayNaviCallback
     */
    @Synchronized
    fun addCallback(callback: DisplayNaviCallback) {
        mCallbacks.add(WeakReference(callback))
    }

    /**
     * 移除回调
     * @param callback 回调
     * @see DisplayNaviCallback
     */
    @Synchronized
    fun removeCallback(callback: DisplayNaviCallback) {
        mCallbacks.removeIf { it.get() == callback }
    }

    /**
     * 释放资源
     */
    @Synchronized
    fun release() {
        releaseTransport()
        releaseConnection()
        mControl.release()
    }

    private fun releaseTransport() {
        isRequestNotification = false
        mTransport?.stopTransport()
        mTransport = null
    }

    private fun releaseConnection() {
        mConnection?.shutdown()
        mConnection = null
    }

    /**
     * 发送再见请求给平台
     */
    fun sendByeByeRequest() {
        if (mTransport?.isConnected() == true) {
            mControl.sendByeByeRequest()
        }
    }

    /**
     * 开始投屏
     * @return 是否成功
     */
    fun startScreenProjection(): Boolean {
        return if (mTransport?.isConnected() == true) {
            mControl.setEncoderState(Protos.VIDEO_FOCUS_PROJECTED, Protos.UNKNOWN)
            true
        } else {
            false
        }
    }

    /**
     * 停止投屏
     * @return 是否成功
     */
    fun stopScreenProjection(): Boolean {
        return if (mTransport?.isConnected() == true) {
            mControl.setEncoderState(Protos.VIDEO_FOCUS_NATIVE, Protos.UNKNOWN)
            true
        } else {
            false
        }
    }

    /**
     * 发送手机屏幕信息给平台
     * @param isLandscape 是否横屏
     * @param rotation 屏幕旋转角度
     */
    fun sendOrientation(isLandscape: Int, rotation: Int) {
        mTransport?.takeIf { it.isConnected() }?.apply {
            mControl.sendOrientation(isLandscape, rotation)
        }
    }

    /**
     * 搜索并连接 WLAN 直连
     * @param address 平台 MAC 地址
     * @param port 端口 默认 30512
     */
    fun startSearchWifiAndConnect(
        context: Context,
        nameBle: String,
        addressBle: String,
        address: String,
        port: Int = DEFAULT_PORT
    ) {
        val configPrefs = RiderService.instance.getConfigPreferences()
        //保存wifi键值对
        configPrefs.setBleName(nameBle)
        configPrefs.setBleAddress(addressBle)
        configPrefs.setWifiAddress(address)
        configPrefs.setWifiPort(port)

        logD(TAG, "connect address:$address port:$port")
        mContext = context
        softP2pManager.start(address, port)
    }

    fun setNaviMode(naviMode: NaviMode) {
        mControl.setNaviMode(naviMode)
    }

    fun setMediaProjection(mediaProjection: MediaProjection) {
        mControl.setMediaProjection(mediaProjection)
    }


    private fun startConnectionEstablishing() {
        if (mConnection == null) {
            mConnection = TcpConnection()
        }
        try {
            mConnection?.start(this)
        } catch (e: IOException) {
            logE(TAG, "startConnectionEstablishing failed", e)
        }
    }

    /**
     * 启动TCP服务器连接（用于AP客户端模式）
     * 在这种模式下，手机作为TCP服务器，仪表端作为TCP客户端连接
     */
    fun startTcpServerConnection() {
        logD(TAG, "Starting TCP server connection for AP client mode")

        // 确保先清理之前的连接
        if (mConnection != null) {
            logD(TAG, "Cleaning up existing connection")
            mConnection?.shutdown()
            mConnection = null
        }

        // 创建专门的TCP服务器连接
        mConnection = TcpServerConnection()

        try {
            mConnection?.start(this)
            logD(TAG, "TCP server connection started successfully")
        } catch (e: IOException) {
            logE(TAG, "Failed to start TCP server connection", e)
            mConnection = null
            // 通知TCP连接失败
            mCallbacks.forEach {
                it.get()?.onTcpConnectionFailed(-7) // TCP服务器启动失败
                it.get()?.onDeviceDisconnected()
            }
        }
    }


    /**
     * 开启与平台的交互
     */
    fun start() {
//        startService()
        startControl()
    }

    private fun startControl() {
        mControl.registerListener(mControlListener)
        mControl.startConnect(mTransport)
    }

    override fun onConnected(transport: Transport) {
        logD(TAG, "socket connected")
        mTransport = transport
        logD("connect analysis:", "tcp connect end::${TimeUtils.getCurrentTimeStr()}")
        mCallbacks.forEach {
            it.get()?.onDeviceConnected()
        }
    }

    /**
     * 重置P2P连接状态
     */
    fun resetP2pState() {
        logD(TAG, "Resetting P2P state")
        softP2pManager.resetConnectionState()
    }

    /**
     * 获取P2P连接信息
     */
    fun getP2pConnectionInfo(): String {
        return softP2pManager.getConnectionInfo()
    }

    @Synchronized
    fun shutdown() {
        releaseTransport()
        releaseConnection()
        softP2pManager.stop()
        mControl.release()
    }

    @Synchronized
    override fun onDisconnected() {
        logD(TAG, "Transport disconnected ${Thread.currentThread().name}")
        mCallbacks.forEach {
            it.get()?.onDeviceDisconnected()
        }
    }

    override fun requestLockScreenDisplay() {
        mControl.requestLockScreenDisplay()
    }

    fun setRequestNotificationSuccess() {
        isRequestNotification = true
    }

    private fun waitForAutoLinkConnect(num: Int) {
        countDown = countDownByFlow(
            10, 1000, mainScope,
            onTick = {
                logD(TAG, "次数：$num,倒计时：$it,Autolink启动情况:$isRequestNotification")
                if (isRequestNotification || !isP2pConnected) {
                    countDown?.cancel()
                }
            }, onFinish = {
                if (!isRequestNotification) {
                    if (num == 6) {
                        logD(TAG, "AutoLink 启动失败，屏蔽投屏导航")
                    } else {
                        logD(TAG, "send AutoLinkConnect")
                        val connect = AutoLinkConnect(getIp())
                        RiderService.instance.sendMessageToRiderService(connect)
                        waitForAutoLinkConnect(num + 1)
                    }
                }
            })
    }

    fun getIp(): String {
        val en: Enumeration<NetworkInterface> = NetworkInterface.getNetworkInterfaces()
        while (en.hasMoreElements()) {
            val networkInterface: NetworkInterface = en.nextElement()
            val enumIpAddress: Enumeration<InetAddress> = networkInterface.inetAddresses
            while (enumIpAddress.hasMoreElements()) {
                val inetAddress: InetAddress = enumIpAddress.nextElement()
                val list2 = inetAddress.hostAddress?.toString()?.split('.')
                if (!inetAddress.isLoopbackAddress && inetAddress is Inet4Address && list2?.size == 4) {
                    if (networkInterface.name.contains("p2p")) {
                        return inetAddress.hostAddress?.toString() ?: ""
                    }
                }
            }
        }
        return ""
    }

    companion object {
        private const val TAG = "DisplayNaviManager"
        private const val DEFAULT_PORT = 30512
    }
}